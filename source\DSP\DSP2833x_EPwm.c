// TI File $Revision: /main/1 $
// Checkin $Date: August 18, 2006   13:46:19 $
// ###########################################################################
//
// FILE:   DSP2833x_EPwm.c
//
// TITLE:  DSP2833x ePWM Initialization & Support Functions.
//
// ###########################################################################
/*******************************************************************************
 * 10KW太阳能逆变器ePWM控制模块
 *
 * 功能概述：
 * 本文件实现了基于TI DSP2833x的10KW太阳能逆变器PWM控制系统，主要包括：
 *
 * 1. 三相SPWM逆变器控制：
 *    - ePWM1/2/3：控制三相逆变器的U/V/W相桥臂
 *    - 采用中心对称PWM模式，PWM频率20kHz
 *    - 集成死区保护，防止上下桥臂直通
 *    - 支持ADC同步采样，实现闭环控制
 *
 * 2. 系统保护功能：
 *    - PWM安全启停时序控制
 *    - 软件强制输出控制
 *    - 与eCAP模块协同的Boost电路控制
 *
 * 3. 技术参数：
 *    - 系统时钟：150MHz
 *    - PWM频率：20kHz (TBCLK=75MHz, TBPRD=1875)
 *    - 死区时间：可配置(DEAD_BAND_DURATION=240，约3.2μs@75MHz)
 *    - 控制方式：中心对称SPWM
 *
 * 4. 硬件接口：
 *    - GPIO0/1：ePWM1A/B (U相上下桥臂)
 *    - GPIO2/3：ePWM2A/B (V相上下桥臂)
 *    - GPIO4/5：ePWM3A/B (W相上下桥臂)
 *    - GPIO6-11：ePWM4-6 (备用或辅助功能)
 *
 * 注意事项：
 * - 调用PWM使能/禁用函数前必须确保系统安全状态
 * - 死区时间设置需根据功率器件特性调整
 * - PWM频率影响系统效率和EMI，需要权衡设计
 *
 * 作者：[项目团队]
 * 版本：v2.0
 * 日期：[当前日期]
 *******************************************************************************/
// $TI Release: 2833x/2823x Header Files V1.32 $
// $Release Date: June 28, 2010 $
// $Copyright:
// Copyright (C) 2009-2021 Texas Instruments Incorporated - http://www.ti.com/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
//
//   Redistributions of source code must retain the above copyright
//   notice, this list of conditions and the following disclaimer.
//
//   Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the
//   documentation and/or other materials provided with the
//   distribution.
//
//   Neither the name of Texas Instruments Incorporated nor the names of
//   its contributors may be used to endorse or promote products derived
//   from this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// $
// ###########################################################################

/*******************************************************************************
 * 包含的头文件
 *******************************************************************************/
#include "DSP2833x_Device.h"   // DSP2833x器件头文件：包含寄存器定义、位域结构等
#include "DSP2833x_Examples.h" // DSP2833x示例头文件：包含通用函数声明和宏定义
#include "SC_define.h"         // 太阳能控制器定义文件：包含PWM周期、死区时间等项目特定参数
//
// InitEPwm - This function initializes the ePWM(s) to a known state.
//
/*void
 InitEPwm(void)
 {
 //
 // Initialize ePWM1/2/3/4/5/6
 //
 }*/
/**
 * @brief  初始化ePWM模块 - 10KW太阳能逆变器PWM控制器配置
 * @note   配置6路ePWM模块用于三相逆变器的SPWM控制
 *         - ePWM1/2/3: 三相逆变器主开关管控制 (U/V/W相)
 *         - ePWM4/5/6: 三相逆变器辅助开关管控制或备用
 *         - 采用中心对称PWM模式，提供死区保护
 *         - PWM频率 = SYSCLKOUT/(2*HSPCLKDIV*CLKDIV*TBPRD) = 150MHz/(2*2*1*1875) = 20kHz
 *         - TBCLK频率 = SYSCLKOUT/HSPCLKDIV = 150MHz/2 = 75MHz
 * @param  None
 * @retval None
 */
void InitEPwm(void)
{ // start of InitEPwm()

    /*
     * 重要：如果要实现所有ePWM模块同步，必须先禁用ePWM模块的时钟
     * 参考：TMS320x2833x, 2823x System Control and Interrupts Reference Guide (Rev. C).pdf
     * 这样可以确保所有PWM模块在重新启用时钟时同时开始计数，实现精确的相位控制
     */

    asm(" EALLOW");                        // 使能对保护寄存器的访问权限
    SysCtrlRegs.PCLKCR0.bit.TBCLKSYNC = 0; // 停止所有ePWM的时基时钟(TBCLK)信号，为同步配置做准备
    asm(" EDIS");                          // 禁用对保护寄存器的访问权限

    /*==========================================================================
     * 配置ePWM1/2/3/4/5/6为中心对称PWM模式
     * 用于三相SPWM逆变器控制，实现高质量的正弦波输出
     *=========================================================================*/

    /*--------------------------------------------------------------------------
     * ePWM1配置 - U相主开关管控制
     * 功能：控制逆变器U相桥臂的上下开关管
     * 输出：EPWM1A(上管), EPWM1B(下管，带死区)
     *--------------------------------------------------------------------------*/
    EPwm1Regs.TBPRD = PWM_HALF_PERIOD;     // 设置PWM周期 = 1875，实际周期 = 2*1875 = 3750个时钟周期
    EPwm1Regs.TBPHS.half.TBPHS = 0x0;     // 时基相位寄存器清零，作为主模块无相位偏移

    /*
     * 时基控制寄存器(TBCTL)配置 - 设置PWM工作模式
     */
    EPwm1Regs.TBCTL.bit.CTRMODE = TB_COUNT_UPDOWN; // 上下计数模式：0→TBPRD→0，产生中心对称PWM
    EPwm1Regs.TBCTL.bit.PHSEN = TB_DISABLE;        // 禁用相位控制，设为主模块(Master)
    EPwm1Regs.TBCTL.bit.PRDLD = TB_SHADOW;         // 使用影子寄存器，周期值在CTR=0时更新
    EPwm1Regs.TBCTL.bit.SYNCOSEL = TB_CTR_ZERO;    // 同步输出选择：当CTR=0时产生同步信号
    EPwm1Regs.TBCTL.bit.SWFSYNC = 0x0;             // 禁用软件强制同步
    EPwm1Regs.TBCTL.bit.HSPCLKDIV = TB_DIV2;       // 高速时钟分频：SYSCLKOUT/2 = 150MHz/2 = 75MHz
    EPwm1Regs.TBCTL.bit.CLKDIV = TB_DIV1;          // 时钟分频：不分频，最终TBCLK = 75MHz
    EPwm1Regs.TBCTL.bit.PHSDIR = TB_DOWN;          // 相位方向：同步后向下计数(主模块中无关紧要)
    EPwm1Regs.TBCTL.bit.FREE_SOFT = 0x3;           // 仿真模式：忽略仿真暂停，PWM继续运行
    /*
     * TBCTL寄存器位域详细说明：
     * bit15-14 (FREE_SOFT): 11 = 忽略仿真暂停，PWM在调试时继续运行
     * bit13 (PHSDIR): 0 = 同步事件后向下计数，1 = 同步事件后向上计数
     * bit12-10 (CLKDIV): 时钟分频，000=÷1, 001=÷2, ..., 111=÷128
     * bit9-7 (HSPCLKDIV): 高速时钟分频，000=÷1, 001=÷2, 010=÷4, ..., 111=÷14
     * bit6 (SWFSYNC): 0 = 无软件同步，1 = 写1产生一次同步脉冲
     * bit5-4 (SYNCOSEL): 同步输出选择，00=EPWMxSYNC, 01=CTR=0, 10=CTR=CMPB, 11=禁用
     * bit3 (PRDLD): 0 = 从影子寄存器加载周期值，1 = 直接加载
     * bit2 (PHSEN): 0 = 禁用相位控制(主模式)，1 = 启用相位控制(从模式)
     * bit1-0 (CTRMODE): 计数模式，00=向上, 01=向下, 10=上下计数, 11=停止
     *
     * 最终TBCLK频率 = SYSCLKOUT/(HSPCLKDIV×CLKDIV) = 150MHz/(2×1) = 75MHz
     * PWM频率 = TBCLK/(2×TBPRD) = 75MHz/(2×1875) = 20kHz
     */

    /*
     * 比较控制寄存器(CMPCTL)配置 - 控制比较值的加载时机
     */
    EPwm1Regs.CMPCTL.bit.LOADAMODE = CC_CTR_ZERO;  // CMPA在CTR=0时从影子寄存器加载
    EPwm1Regs.CMPCTL.bit.LOADBMODE = CC_CTR_ZERO;  // CMPB在CTR=0时从影子寄存器加载
    // EPwm1Regs.CMPA.half.CMPA = 200;             // 比较值A，控制占空比(此处注释掉，运行时动态设置)
    // EPwm1Regs.CMPB = 200;                       // 比较值B，控制占空比(此处注释掉，运行时动态设置)
    EPwm1Regs.CMPCTL.bit.SHDWAMODE = CC_SHADOW;    // CMPA使用影子寄存器模式
    EPwm1Regs.CMPCTL.bit.SHDWBMODE = CC_SHADOW;    // CMPB使用影子寄存器模式
    /*
     * CMPCTL寄存器位域详细说明：
     * bit15-10: 保留位
     * bit9 (SHDWBFULL): CMPB影子寄存器满标志(只读)
     * bit8 (SHDWAFULL): CMPA影子寄存器满标志(只读)
     * bit7,5: 保留位
     * bit6 (SHDWBMODE): 0=影子模式, 1=立即模式
     * bit4 (SHDWAMODE): 0=影子模式, 1=立即模式
     * bit3-2 (LOADBMODE): CMPB加载模式，00=CTR=0时, 01=CTR=PRD时, 10=CTR=0或PRD时, 11=禁用
     * bit1-0 (LOADAMODE): CMPA加载模式，00=CTR=0时, 01=CTR=PRD时, 10=CTR=0或PRD时, 11=禁用
     */

    /*
     * 动作限定器控制寄存器A(AQCTLA)配置 - 控制EPWM1A输出波形
     * 实现中心对称PWM：上升沿清零，下降沿置位
     */
    EPwm1Regs.AQCTLA.bit.CAU = AQ_CLEAR;           // 计数器递增且等于CMPA时，EPWM1A输出清零(低电平)
    EPwm1Regs.AQCTLA.bit.CAD = AQ_SET;             // 计数器递减且等于CMPA时，EPWM1A输出置位(高电平)
    /*
     * AQCTLA寄存器位域详细说明：
     * bit15-12: 保留位
     * bit11-10 (CBD): 计数器递减且等于CMPB时的动作，00=无动作
     * bit9-8 (CBU): 计数器递增且等于CMPB时的动作，00=无动作
     * bit7-6 (CAD): 计数器递减且等于CMPA时的动作，10=置位
     * bit5-4 (CAU): 计数器递增且等于CMPA时的动作，01=清零
     * bit3-2 (PRD): 计数器等于周期值时的动作，00=无动作
     * bit1-0 (ZRO): 计数器等于零时的动作，00=无动作
     */

    /*
     * 动作限定器控制寄存器B(AQCTLB)配置 - 控制EPWM1B输出波形
     * EPWM1B与EPWM1A互补，用于下桥臂开关管控制
     */
    EPwm1Regs.AQCTLB.bit.CBU = AQ_CLEAR;           // 计数器递增且等于CMPB时，EPWM1B输出清零
    EPwm1Regs.AQCTLB.bit.CBD = AQ_SET;             // 计数器递减且等于CMPB时，EPWM1B输出置位
    /*
     * AQCTLB寄存器位域说明：
     * bit11-10 (CBD): 计数器递减且等于CMPB时的动作，10=置位
     * bit9-8 (CBU): 计数器递增且等于CMPB时的动作，01=清零
     * 其他位与AQCTLA类似
     */

    /*
     * 动作限定器软件强制寄存器(AQSFRC)配置
     * 控制软件强制输出的加载时机
     */
    EPwm1Regs.AQSFRC.bit.RLDCSF = 0x3;            // 立即加载软件强制值
    /*
     * RLDCSF位域说明：
     * 00: 计数器等于零时加载
     * 01: 计数器等于周期值时加载
     * 10: 计数器等于零或周期值时加载
     * 11: 立即加载
     */

    /*
     * 死区发生器配置 - 防止上下桥臂同时导通
     * 死区时间 = DEAD_BAND_DURATION × TBCLK周期 = 240 × (1/75MHz) = 3.2μs
     */
    EPwm1Regs.DBFED = DEAD_BAND_DURATION;          // 下降沿死区时间(FED - Falling Edge Delay)
    EPwm1Regs.DBRED = DEAD_BAND_DURATION;          // 上升沿死区时间(RED - Rising Edge Delay)

    /*
     * PWM斩波器控制 - 通常用于电机控制中的电流限制
     * 在逆变器应用中一般禁用
     */
    EPwm1Regs.PCCTL.bit.CHPEN = CHP_DISABLE;       // 禁用PWM斩波器单元

    /*
     * 事件触发器选择寄存器(ETSEL)配置 - 用于触发ADC采样
     * 在逆变器中用于同步电流/电压采样
     */
    EPwm1Regs.ETSEL.bit.SOCAEN = 1;                // 使能SOCA事件到ADC
    EPwm1Regs.ETSEL.bit.SOCASEL = ET_CTR_ZERO;     // SOCA触发条件：计数器等于零时触发ADC采样
    EPwm1Regs.ETSEL.bit.SOCBSEL = ET_CTR_ZERO;     // SOCB触发条件：计数器等于零时(备用)
    /*
     * ETSEL寄存器位域详细说明：
     * bit15 (SOCBEN): 0=禁用SOCB, 1=使能SOCB
     * bit14-12 (SOCBSEL): SOCB触发选择，000=保留, 001=CTR=0, 010=CTR=PRD,
     *                     100=CMPA递增, 101=CMPA递减, 110=CMPB递增, 111=CMPB递减
     * bit11 (SOCAEN): 1=使能SOCA, 0=禁用SOCA
     * bit10-8 (SOCASEL): SOCA触发选择，同SOCBSEL
     * bit7-4: 保留位
     * bit3 (INTEN): EPWMx中断使能，0=禁用, 1=使能
     * bit2-0 (INTSEL): 中断触发选择，001=CTR=0, 010=CTR=PRD, 100=CMPA递增,
     *                  101=CMPA递减, 110=CMPB递增, 111=CMPB递减
     */

    /*
     * 事件触发器预分频寄存器(ETPS)配置
     * 控制ADC触发的频率
     */
    EPwm1Regs.ETPS.bit.SOCAPRD = ET_1ST;           // 每发生1次触发事件就产生1个SOCA脉冲
    /*
     * ETPS寄存器位域详细说明：
     * bit15-14 (EPWMxSOCB): SOCB事件计数器(只读)
     * bit13-12 (SOCBPRD): SOCB预分频，00=禁用, 01=每1次事件, 10=每2次事件, 11=每3次事件
     * bit11-10 (EPWMxSOCA): SOCA事件计数器(只读)
     * bit9-8 (SOCAPRD): SOCA预分频，00=禁用, 01=每1次事件, 10=每2次事件, 11=每3次事件
     * bit7-4: 保留位
     * bit3-2 (INTCNT): 中断事件计数器(只读)
     * bit1-0 (INTPRD): 中断预分频，00=禁用, 01=每1次事件, 10=每2次事件, 11=每3次事件
     */

    /*--------------------------------------------------------------------------
     * ePWM2配置 - V相主开关管控制
     * 功能：控制逆变器V相桥臂的上下开关管
     * 输出：EPWM2A(上管), EPWM2B(下管，带死区)
     * 配置与ePWM1基本相同，但同步信号来源不同
     *--------------------------------------------------------------------------*/
    EPwm2Regs.TBPRD = PWM_HALF_PERIOD;             // 设置PWM周期，与ePWM1相同
    EPwm2Regs.TBPHS.half.TBPHS = 0x0;             // 时基相位寄存器清零

    /*
     * ePWM2时基控制寄存器配置
     */
    EPwm2Regs.TBCTL.bit.CTRMODE = TB_COUNT_UPDOWN; // 上下计数模式，与ePWM1相同
    EPwm2Regs.TBCTL.bit.PHSEN = TB_DISABLE;        // 禁用相位控制(注：实际应用中可能需要启用)
    EPwm2Regs.TBCTL.bit.PRDLD = TB_SHADOW;         // 使用影子寄存器
    EPwm2Regs.TBCTL.bit.SYNCOSEL = TB_SYNC_IN;     // 同步输出选择：传递输入同步信号
    EPwm2Regs.TBCTL.bit.SWFSYNC = 0x0;             // 禁用软件强制同步
    EPwm2Regs.TBCTL.bit.HSPCLKDIV = TB_DIV2;       // 高速时钟分频：÷2
    EPwm2Regs.TBCTL.bit.CLKDIV = TB_DIV1;          // 时钟分频：不分频
    EPwm2Regs.TBCTL.bit.PHSDIR = TB_DOWN;          // 相位方向：向下计数
    EPwm2Regs.TBCTL.bit.FREE_SOFT = 0x3;           // 仿真模式：忽略仿真暂停
    /*
     * 注意：ePWM2与ePWM1的主要区别在于同步配置
     * - ePWM1是主模块(Master)，产生同步信号
     * - ePWM2是从模块，接收ePWM1的同步信号
     * - 在三相逆变器中，通常需要120°相位差，但此处配置为同相
     * - 实际应用中可能需要通过TBPHS寄存器设置相位偏移
     */

    /*
     * ePWM2比较控制寄存器配置 - 与ePWM1相同
     */
    EPwm2Regs.CMPCTL.bit.LOADAMODE = CC_CTR_ZERO;  // CMPA在CTR=0时加载
    EPwm2Regs.CMPCTL.bit.LOADBMODE = CC_CTR_ZERO;  // CMPB在CTR=0时加载
    EPwm2Regs.CMPCTL.bit.SHDWAMODE = CC_SHADOW;    // CMPA使用影子寄存器
    EPwm2Regs.CMPCTL.bit.SHDWBMODE = CC_SHADOW;    // CMPB使用影子寄存器
    // EPwm2Regs.CMPCTL.all = 0x0005;            // Compare control register
    // bit15-10  0:      reserved
    // bit9      0:      SHDWBFULL, read-only
    // bit8      0:      SHDWAFULL, read-only
    // bit7      0:      reserved
    // bit6      0:      SHDWBMODE, 0 = shadow mode, 1= immediate
    // bit5      0:      reserved
    // bit4      0:      SHDWAMODE, 0 = shadow mode, 1= immediate
    // bit3-2    01:     LOADBMODE, 00 => CTR=0; 01 =>CTR=PRD,
    // 10 => CTR=0 or CTR=PRD, 11, disable
    // bit1-0    01:     LOADAMODE, 00 => CTR=0; 01 =>CTR=PRD,
    // 10 => CTR=0 or CTR=PRD, 11, disable

    // config EPWM1A output when(1)incrementing and (2)equals CMPA
    EPwm2Regs.AQCTLA.bit.CAU = AQ_CLEAR;
    // config EPWM1A output when(1)decrementing and (2)equals CMPA
    EPwm2Regs.AQCTLA.bit.CAD = AQ_SET;
    // Action-qualifier control register A
    // bit15-12     0000:   reserved
    // bit11-10     00:     CBD, 00 = do nothing
    // bit9-8       00:     CBU, 00 = do nothing
    // bit7-6       01:     CAD, 00 = disable, 01, clear, 10 = set, 11 = toggle
    // bit5-4       10:     CAU, 00 = disable, 01, clear, 10 = set, 11 = toggle
    // bit3-2       00:     PRD, 00 = do nothing
    // bit1-0       00:     ZRO, 00 = do nothing
    //  config EPWM1A output when(1)incrementing and (2)equals CMPA
    EPwm2Regs.AQCTLB.bit.CBU = AQ_CLEAR;
    // config EPWM1A output when(1)decrementing and (2)equals CMPA
    EPwm2Regs.AQCTLB.bit.CBD = AQ_SET;
    // Action-qualifier control register A
    // bit15-12     0000:   reserved
    // bit11-10     00:     CBD, 00 = do nothing
    // bit9-8       00:     CBU, 00 = do nothing
    // bit7-6       01:     CAD, 00 = disable, 01, clear, 10 = set, 11 = toggle
    // bit5-4       10:     CAU, 00 = disable, 01, clear, 10 = set, 11 = toggle
    // bit3-2       00:     PRD, 00 = do nothing
    // bit1-0       00:     ZRO, 00 = do nothing

    // AQCSFRC load mode
    EPwm2Regs.AQSFRC.bit.RLDCSF = 0x3;
    // 00 Load on event counter equauls zero
    // 01 Load on event counter equauls period
    // 10 Load on event counter equauls zero or period
    // 11 Load immediately

    EPwm2Regs.DBFED = DEAD_BAND_DURATION; // 下降沿死区时间：240个TBCLK周期 = 3.2μs@75MHz
    EPwm2Regs.DBRED = DEAD_BAND_DURATION; // 上升沿死区时间：240个TBCLK周期 = 3.2μs@75MHz

    EPwm2Regs.PCCTL.bit.CHPEN = CHP_DISABLE; // PWM chopper unit disabled

    // ePWM3 Configuration
    EPwm3Regs.TBPRD = PWM_HALF_PERIOD; // actual period = 2*PWM_HALF_PERIOD
    EPwm3Regs.TBPHS.half.TBPHS = 0x0;  // Set timer phase, zero

    EPwm3Regs.TBCTL.bit.CTRMODE = TB_COUNT_UPDOWN; // up-down mode
    EPwm3Regs.TBCTL.bit.PHSEN = TB_DISABLE;        // Master module
    EPwm3Regs.TBCTL.bit.PRDLD = TB_SHADOW;         // reload from shadow
    EPwm3Regs.TBCTL.bit.SYNCOSEL = TB_SYNC_IN;     // sync when CTR to zero
    EPwm3Regs.TBCTL.bit.SWFSYNC = 0x0;
    EPwm3Regs.TBCTL.bit.HSPCLKDIV = TB_DIV2; // prescaler = 2
    EPwm3Regs.TBCTL.bit.CLKDIV = TB_DIV1;
    EPwm3Regs.TBCTL.bit.PHSDIR = TB_DOWN; // don't care
    EPwm3Regs.TBCTL.bit.FREE_SOFT = 0x3;
    // bit15-14     11:      FREE/SOFT, 11 = ignore emulation suspend
    // bit13        0:       PHSDIR, 0 = count down after sync event,
    // 1 = count up after sync event
    // bit12-10     000:     CLKDIV, 000 = 2^0, 001 = 2^1, ..., 111 = 2^7,
    // TBCLK = SYSCLKOUT/(HSPCLKDIV*CLKDIV)
    // bit9-7       001:     HSPCLKDIV, Prescaler, 000 = 1, 001 = 2,
    // 010 = 4, ..., 110 = 12, 111 = 14.
    // bit6         0:       SWFSYNC, 0 = no software sync produced,
    // 1 = writing a 1 forces a one-time sync pulse
    // bit5-4       01:      SYNCOSEL, sync-output-select, 00 = EPWMxSYNC,
    // 01 =>CTR=0, 10 =>CTR=CMPB, 11 = sync-out disabled
    // bit3         0:       PRDLD, 0 = reload PRD on counter=0 from shadow,
    // 1 = without shadow
    // bit2         0:       PHSEN, 0 = phase control disabled, master mode,
    // 1 = slave mode
    // bit1-0       11:      CTRMODE, Counter Mode, 00 = Up, 01 = Down,
    // 10 = Up-Down, 11 = Stop timer stopped (disabled)
    EPwm3Regs.CMPCTL.bit.LOADAMODE = CC_CTR_ZERO;
    EPwm3Regs.CMPCTL.bit.LOADBMODE = CC_CTR_ZERO;
    EPwm3Regs.CMPCTL.bit.SHDWAMODE = CC_SHADOW;
    EPwm3Regs.CMPCTL.bit.SHDWBMODE = CC_SHADOW;
    // EPwm3Regs.CMPCTL.all = 0x0005;            // Compare control register
    // bit15-10  0:      reserved
    // bit9      0:      SHDWBFULL, read-only
    // bit8      0:      SHDWAFULL, read-only
    // bit7      0:      reserved
    // bit6      0:      SHDWBMODE, 0 = shadow mode, 1= immediate
    // bit5      0:      reserved
    // bit4      0:      SHDWAMODE, 0 = shadow mode, 1= immediate
    // bit3-2    01:     LOADBMODE, 00 => CTR=0; 01 =>CTR=PRD,
    // 10 => CTR=0 or CTR=PRD, 11, disable
    // bit1-0    01:     LOADAMODE, 00 => CTR=0; 01 =>CTR=PRD,
    // 10 => CTR=0 or CTR=PRD, 11, disable

    // config EPWM3A output when(1)incrementing and (2)equals CMPA
    EPwm3Regs.AQCTLA.bit.CAU = AQ_CLEAR;
    // config EPWM3A output when(1)decrementing and (2)equals CMPA
    EPwm3Regs.AQCTLA.bit.CAD = AQ_SET;
    // Action-qualifier control register A
    // bit15-12     0000:   reserved
    // bit11-10     00:     CBD, 00 = do nothing
    // bit9-8       00:     CBU, 00 = do nothing
    // bit7-6       01:     CAD, 00 = disable, 01, clear, 10 = set, 11 = toggle
    // bit5-4       10:     CAU, 00 = disable, 01, clear, 10 = set, 11 = toggle
    // bit3-2       00:     PRD, 00 = do nothing
    // bit1-0       00:     ZRO, 00 = do nothing
    EPwm3Regs.AQCTLB.bit.CBU = AQ_CLEAR;
    // config EPWM1A output when(1)decrementing and (2)equals CMPA
    EPwm3Regs.AQCTLB.bit.CBD = AQ_SET;
    // Action-qualifier control register A
    // bit15-12     0000:   reserved
    // bit11-10     00:     CBD, 00 = do nothing
    // bit9-8       00:     CBU, 00 = do nothing
    // bit7-6       01:     CAD, 00 = disable, 01, clear, 10 = set, 11 = toggle
    // bit5-4       10:     CAU, 00 = disable, 01, clear, 10 = set, 11 = toggle
    // bit3-2       00:     PRD, 00 = do nothing
    // bit1-0       00:     ZRO, 00 = do nothing
    //  AQCSFRC load mode
    EPwm3Regs.AQSFRC.bit.RLDCSF = 0x3;
    // 00 Load on event counter equauls zero
    // 01 Load on event counter equauls period
    // 10 Load on event counter equauls zero or period
    // 11 Load immediately

    EPwm3Regs.DBFED = DEAD_BAND_DURATION; // 下降沿死区时间：240个TBCLK周期 = 3.2μs@75MHz
    EPwm3Regs.DBRED = DEAD_BAND_DURATION; // 上升沿死区时间：240个TBCLK周期 = 3.2μs@75MHz

    EPwm3Regs.PCCTL.bit.CHPEN = CHP_DISABLE; // PWM chopper unit disabled

    // ePWM4 Configuration
    EPwm4Regs.TBPRD = PWM_HALF_PERIOD; // actual period = 2*PWM_HALF_PERIOD
    EPwm4Regs.TBPHS.half.TBPHS = 0x0;  // Set timer phase, zero

    EPwm4Regs.TBCTL.bit.CTRMODE = TB_COUNT_UPDOWN; // up-down mode
    EPwm4Regs.TBCTL.bit.PHSEN = TB_DISABLE;        // Master module
    EPwm4Regs.TBCTL.bit.PRDLD = TB_SHADOW;         // reload from shadow
    EPwm4Regs.TBCTL.bit.SYNCOSEL = TB_SYNC_IN;     // sync when CTR to zero
    EPwm4Regs.TBCTL.bit.SWFSYNC = 0x0;
    EPwm4Regs.TBCTL.bit.HSPCLKDIV = TB_DIV2; // prescaler = 2
    EPwm4Regs.TBCTL.bit.CLKDIV = TB_DIV1;
    EPwm4Regs.TBCTL.bit.PHSDIR = TB_DOWN; // don't care
    EPwm4Regs.TBCTL.bit.FREE_SOFT = 0x3;
    // bit15-14     11:      FREE/SOFT, 11 = ignore emulation suspend
    // bit13        0:       PHSDIR, 0 = count down after sync event,
    // 1 = count up after sync event
    // bit12-10     000:     CLKDIV, 000 = 2^0, 001 = 2^1, ..., 111 = 2^7,
    // TBCLK = SYSCLKOUT/(HSPCLKDIV*CLKDIV)
    // bit9-7       001:     HSPCLKDIV, Prescaler, 000 = 1, 001 = 2,
    // 010 = 4, ..., 110 = 12, 111 = 14.
    // bit6         0:       SWFSYNC, 0 = no software sync produced,
    // 1 = writing a 1 forces a one-time sync pulse
    // bit5-4       01:      SYNCOSEL, sync-output-select, 00 = EPWMxSYNC,
    // 01 =>CTR=0, 10 =>CTR=CMPB, 11 = sync-out disabled
    // bit3         0:       PRDLD, 0 = reload PRD on counter=0 from shadow,
    // 1 = without shadow
    // bit2         0:       PHSEN, 0 = phase control disabled, master mode,
    // 1 = slave mode
    // bit1-0       11:      CTRMODE, Counter Mode, 00 = Up, 01 = Down,
    // 10 = Up-Down, 11 = Stop timer stopped (disabled)
    EPwm4Regs.CMPCTL.bit.LOADAMODE = CC_CTR_ZERO;
    EPwm4Regs.CMPCTL.bit.LOADBMODE = CC_CTR_ZERO;
    EPwm4Regs.CMPCTL.bit.SHDWAMODE = CC_SHADOW;
    EPwm4Regs.CMPCTL.bit.SHDWBMODE = CC_SHADOW;
    // EPwm4Regs.CMPCTL.all = 0x0005;            // Compare control register
    // bit15-10  0:      reserved
    // bit9      0:      SHDWBFULL, read-only
    // bit8      0:      SHDWAFULL, read-only
    // bit7      0:      reserved
    // bit6      0:      SHDWBMODE, 0 = shadow mode, 1= immediate
    // bit5      0:      reserved
    // bit4      0:      SHDWAMODE, 0 = shadow mode, 1= immediate
    // bit3-2    01:     LOADBMODE, 00 => CTR=0; 01 =>CTR=PRD,
    // 10 => CTR=0 or CTR=PRD, 11, disable
    // bit1-0    01:     LOADAMODE, 00 => CTR=0; 01 =>CTR=PRD,
    // 10 => CTR=0 or CTR=PRD, 11, disable

    // config EPWM1A output when(1)incrementing and (2)equals CMPA
    EPwm4Regs.AQCTLA.bit.CAU = AQ_CLEAR;
    // config EPWM1A output when(1)decrementing and (2)equals CMPA
    EPwm4Regs.AQCTLA.bit.CAD = AQ_SET;
    // Action-qualifier control register A
    // bit15-12     0000:   reserved
    // bit11-10     00:     CBD, 00 = do nothing
    // bit9-8       00:     CBU, 00 = do nothing
    // bit7-6       01:     CAD, 00 = disable, 01, clear, 10 = set, 11 = toggle
    // bit5-4       10:     CAU, 00 = disable, 01, clear, 10 = set, 11 = toggle
    // bit3-2       00:     PRD, 00 = do nothing
    // bit1-0       00:     ZRO, 00 = do nothing
    EPwm4Regs.AQCTLB.bit.CBU = AQ_CLEAR;
    // config EPWM1A output when(1)decrementing and (2)equals CMPA
    EPwm4Regs.AQCTLB.bit.CBD = AQ_SET;
    // Action-qualifier control register A
    // bit15-12     0000:   reserved
    // bit11-10     00:     CBD, 00 = do nothing
    // bit9-8       00:     CBU, 00 = do nothing
    // bit7-6       01:     CAD, 00 = disable, 01, clear, 10 = set, 11 = toggle
    // bit5-4       10:     CAU, 00 = disable, 01, clear, 10 = set, 11 = toggle
    // bit3-2       00:     PRD, 00 = do nothing
    // bit1-0       00:     ZRO, 00 = do nothing
    EPwm4Regs.AQSFRC.bit.RLDCSF = 0x3;
    // 00 Load on event counter equauls zero
    // 01 Load on event counter equauls period
    // 10 Load on event counter equauls zero or period
    // 11 Load immediately
    //  force AQC output low

    EPwm4Regs.DBFED = DEAD_BAND_DURATION; // 下降沿死区时间：240个TBCLK周期 = 3.2μs@75MHz
    EPwm4Regs.DBRED = DEAD_BAND_DURATION; // 上升沿死区时间：240个TBCLK周期 = 3.2μs@75MHz

    EPwm4Regs.PCCTL.bit.CHPEN = CHP_DISABLE; // PWM chopper unit disabled

    // ePWM5 Configuration
    EPwm5Regs.TBPRD = PWM_HALF_PERIOD; // actual period = 2*PWM_HALF_PERIOD
    EPwm5Regs.TBPHS.half.TBPHS = 0x0;  // Set timer phase, zero

    EPwm5Regs.TBCTL.bit.CTRMODE = TB_COUNT_UPDOWN; // up-down mode
    EPwm5Regs.TBCTL.bit.PHSEN = TB_DISABLE;        // Master module
    EPwm5Regs.TBCTL.bit.PRDLD = TB_SHADOW;         // reload from shadow
    EPwm5Regs.TBCTL.bit.SYNCOSEL = TB_SYNC_IN;     // sync when CTR to zero
    EPwm5Regs.TBCTL.bit.SWFSYNC = 0x0;
    EPwm5Regs.TBCTL.bit.HSPCLKDIV = TB_DIV2; // prescaler = 2
    EPwm5Regs.TBCTL.bit.CLKDIV = TB_DIV1;
    EPwm5Regs.TBCTL.bit.PHSDIR = TB_DOWN; // don't care
    EPwm5Regs.TBCTL.bit.FREE_SOFT = 0x3;
    // bit15-14     11:      FREE/SOFT, 11 = ignore emulation suspend
    // bit13        0:       PHSDIR, 0 = count down after sync event,
    // 1 = count up after sync event
    // bit12-10     000:     CLKDIV, 000 = 2^0, 001 = 2^1, ..., 111 = 2^7,
    // TBCLK = SYSCLKOUT/(HSPCLKDIV*CLKDIV)
    // bit9-7       001:     HSPCLKDIV, Prescaler, 000 = 1, 001 = 2,
    // 010 = 4, ..., 110 = 12, 111 = 14.
    // bit6         0:       SWFSYNC, 0 = no software sync produced,
    // 1 = writing a 1 forces a one-time sync pulse
    // bit5-4       00:      SYNCOSEL, sync-output-select, 00 = EPWMxSYNC,
    // 01 =>CTR=0, 10 =>CTR=CMPB, 11 = sync-out disabled
    // bit3         0:       PRDLD, 0 = reload PRD on counter=0 from shadow,
    // 1 = without shadow
    // bit2         0:       PHSEN, 0 = phase control disabled, master mode,
    // 1 = slave mode
    // bit1-0       11:      CTRMODE, Counter Mode, 00 = Up, 01 = Down,
    // 10 = Up-Down, 11 = Stop timer stopped (disabled)
    EPwm5Regs.CMPCTL.bit.LOADAMODE = CC_CTR_ZERO;
    EPwm5Regs.CMPCTL.bit.LOADBMODE = CC_CTR_ZERO;
    EPwm5Regs.CMPCTL.bit.SHDWAMODE = CC_SHADOW;
    EPwm5Regs.CMPCTL.bit.SHDWBMODE = CC_SHADOW;
    // EPwm5Regs.CMPCTL.all = 0x0005;            // Compare control register
    // bit15-10  0:      reserved
    // bit9      0:      SHDWBFULL, read-only
    // bit8      0:      SHDWAFULL, read-only
    // bit7      0:      reserved
    // bit6      0:      SHDWBMODE, 0 = shadow mode, 1= immediate
    // bit5      0:      reserved
    // bit4      0:      SHDWAMODE, 0 = shadow mode, 1= immediate
    // bit3-2    01:     LOADBMODE, 00 => CTR=0; 01 =>CTR=PRD,
    // 10 => CTR=0 or CTR=PRD, 11, disable
    // bit1-0    01:     LOADAMODE, 00 => CTR=0; 01 =>CTR=PRD,
    // 10 => CTR=0 or CTR=PRD, 11, disable

    // config EPWM1A output when(1)incrementing and (2)equals CMPA
    EPwm5Regs.AQCTLA.bit.CAU = AQ_CLEAR;
    // config EPWM1A output when(1)decrementing and (2)equals CMPA
    EPwm5Regs.AQCTLA.bit.CAD = AQ_SET;
    // Action-qualifier control register A
    // bit15-12     0000:   reserved
    // bit11-10     00:     CBD, 00 = do nothing
    // bit9-8       00:     CBU, 00 = do nothing
    // bit7-6       01:     CAD, 00 = disable, 01, clear, 10 = set, 11 = toggle
    // bit5-4       10:     CAU, 00 = disable, 01, clear, 10 = set, 11 = toggle
    // bit3-2       00:     PRD, 00 = do nothing
    // bit1-0       00:     ZRO, 00 = do nothing
    EPwm5Regs.AQCTLB.bit.CBU = AQ_CLEAR;
    // config EPWM1A output when(1)decrementing and (2)equals CMPA
    EPwm5Regs.AQCTLB.bit.CBD = AQ_SET;
    // Action-qualifier control register A
    // bit15-12     0000:   reserved
    // bit11-10     00:     CBD, 00 = do nothing
    // bit9-8       00:     CBU, 00 = do nothing
    // bit7-6       01:     CAD, 00 = disable, 01, clear, 10 = set, 11 = toggle
    // bit5-4       10:     CAU, 00 = disable, 01, clear, 10 = set, 11 = toggle
    // bit3-2       00:     PRD, 00 = do nothing
    // bit1-0       00:     ZRO, 00 = do nothing
    EPwm5Regs.AQSFRC.bit.RLDCSF = 0x3;
    // 00 Load on event counter equauls zero
    // 01 Load on event counter equauls period
    // 10 Load on event counter equauls zero or period
    // 11 Load immediately
    //  force AQC output low

    EPwm5Regs.DBFED = DEAD_BAND_DURATION; // 下降沿死区时间：240个TBCLK周期 = 3.2μs@75MHz
    EPwm5Regs.DBRED = DEAD_BAND_DURATION; // 上升沿死区时间：240个TBCLK周期 = 3.2μs@75MHz

    EPwm5Regs.PCCTL.bit.CHPEN = CHP_DISABLE; // PWM chopper unit disabled

    // ePWM6 Configuration
    EPwm6Regs.TBPRD = PWM_HALF_PERIOD; // actual period = 2*PWM_HALF_PERIOD
    EPwm6Regs.TBPHS.half.TBPHS = 0x0;  // Set timer phase, zero

    EPwm6Regs.TBCTL.bit.CTRMODE = TB_COUNT_UPDOWN; // up-down mode
    EPwm6Regs.TBCTL.bit.PHSEN = TB_DISABLE;        // master module
    EPwm6Regs.TBCTL.bit.PRDLD = TB_SHADOW;         // reload from shadow
    EPwm6Regs.TBCTL.bit.SYNCOSEL = TB_SYNC_IN;     // sync when CTR to zero
    EPwm6Regs.TBCTL.bit.SWFSYNC = 0x0;
    EPwm6Regs.TBCTL.bit.HSPCLKDIV = TB_DIV2; // prescaler = 2
    EPwm6Regs.TBCTL.bit.CLKDIV = TB_DIV1;
    EPwm6Regs.TBCTL.bit.PHSDIR = TB_DOWN; // don't care
    EPwm6Regs.TBCTL.bit.FREE_SOFT = 0x3;
    // bit15-14     11:      FREE/SOFT, 11 = ignore emulation suspend
    // bit13        0:       PHSDIR, 0 = count down after sync event,
    // 1 = count up after sync event
    // bit12-10     000:     CLKDIV, 000 = 2^0, 001 = 2^1, ..., 111 = 2^7,
    // TBCLK = SYSCLKOUT/(HSPCLKDIV*CLKDIV)
    // bit9-7       001:     HSPCLKDIV, Prescaler, 000 = 1, 001 = 2,
    // 010 = 4, ..., 110 = 12, 111 = 14.
    // bit6         0:       SWFSYNC, 0 = no software sync produced,
    // 1 = writing a 1 forces a one-time sync pulse
    // bit5-4       01:      SYNCOSEL, sync-output-select, 00 = EPWMxSYNC,
    // 01 =>CTR=0, 10 =>CTR=CMPB, 11 = sync-out disabled
    // bit3         0:       PRDLD, 0 = reload PRD on counter=0 from shadow,
    // 1 = without shadow
    // bit2         0:       PHSEN, 0 = phase control disabled, master mode,
    // 1 = slave mode
    // bit1-0       11:      CTRMODE, Counter Mode, 00 = Up, 01 = Down,
    // 10 = Up-Down, 11 = Stop timer stopped (disabled)
    EPwm6Regs.CMPCTL.bit.LOADAMODE = CC_CTR_ZERO;
    EPwm6Regs.CMPCTL.bit.LOADBMODE = CC_CTR_ZERO;
    EPwm6Regs.CMPCTL.bit.SHDWAMODE = CC_SHADOW;
    EPwm6Regs.CMPCTL.bit.SHDWBMODE = CC_SHADOW;
    // EPwm6Regs.CMPCTL.all = 0x0005;            // Compare control register
    // bit15-10  0:      reserved
    // bit9      0:      SHDWBFULL, read-only
    // bit8      0:      SHDWAFULL, read-only
    // bit7      0:      reserved
    // bit6      0:      SHDWBMODE, 0 = shadow mode, 1= immediate
    // bit5      0:      reserved
    // bit4      0:      SHDWAMODE, 0 = shadow mode, 1= immediate
    // bit3-2    01:     LOADBMODE, 00 => CTR=0; 01 =>CTR=PRD,
    // 10 => CTR=0 or CTR=PRD, 11, disable
    // bit1-0    01:     LOADAMODE, 00 => CTR=0; 01 =>CTR=PRD,
    // 10 => CTR=0 or CTR=PRD, 11, disable

    // config EPWM1A output when(1)incrementing and (2)equals CMPA
    EPwm6Regs.AQCTLA.bit.CAU = AQ_CLEAR;
    // config EPWM1A output when(1)decrementing and (2)equals CMPA
    EPwm6Regs.AQCTLA.bit.CAD = AQ_SET;
    // Action-qualifier control register A
    // bit15-12     0000:   reserved
    // bit11-10     00:     CBD, 00 = do nothing
    // bit9-8       00:     CBU, 00 = do nothing
    // bit7-6       01:     CAD, 00 = disable, 01, clear, 10 = set, 11 = toggle
    // bit5-4       10:     CAU, 00 = disable, 01, clear, 10 = set, 11 = toggle
    // bit3-2       00:     PRD, 00 = do nothing
    // bit1-0       00:     ZRO, 00 = do nothing
    EPwm6Regs.AQCTLB.bit.CBU = AQ_CLEAR;
    // config EPWM1A output when(1)decrementing and (2)equals CMPA
    EPwm6Regs.AQCTLB.bit.CBD = AQ_SET;
    // Action-qualifier control register A
    // bit15-12     0000:   reserved
    // bit11-10     00:     CBD, 00 = do nothing
    // bit9-8       00:     CBU, 00 = do nothing
    // bit7-6       01:     CAD, 00 = disable, 01, clear, 10 = set, 11 = toggle
    // bit5-4       10:     CAU, 00 = disable, 01, clear, 10 = set, 11 = toggle
    // bit3-2       00:     PRD, 00 = do nothing
    // bit1-0       00:     ZRO, 00 = do nothing
    EPwm6Regs.AQSFRC.bit.RLDCSF = 0x3;
    // 00 Load on event counter equauls zero
    // 01 Load on event counter equauls period
    // 10 Load on event counter equauls zero or period
    // 11 Load immediately
    //  force AQC output low

    EPwm6Regs.DBFED = DEAD_BAND_DURATION; // 下降沿死区时间：240个TBCLK周期 = 3.2μs@75MHz
    EPwm6Regs.DBRED = DEAD_BAND_DURATION; // 上升沿死区时间：240个TBCLK周期 = 3.2μs@75MHz

    EPwm6Regs.PCCTL.bit.CHPEN = CHP_DISABLE; // PWM chopper unit disabled

    // initial TB counter
    EPwm1Regs.TBCTR = 0x0000;              // Clear timer counter
    EPwm2Regs.TBCTR = 0x0000;              // Clear timer counter
    EPwm3Regs.TBCTR = 0x0000;              // Clear timer counter
    EPwm4Regs.TBCTR = 0x0000;              // Clear timer counter
    EPwm5Regs.TBCTR = 0x0000;              // Clear timer counter
    EPwm6Regs.TBCTR = 0x0000;              // Clear timer counter
    asm(" EALLOW");
    // Enable EALLOW protected register access
    SysCtrlRegs.PCLKCR0.bit.TBCLKSYNC = 1; // HSPCLK to ePWM modules enabled
    asm(" EDIS");
    // Disable EALLOW protected register access

    EPwm1Regs.DBCTL.bit.OUT_MODE = DB_FULL_ENABLE;
    EPwm1Regs.DBCTL.bit.POLSEL = DB_ACTV_HI;   // DB_ACTV_HIC;
    EPwm1Regs.DBCTL.bit.IN_MODE = DBA_ALL;     // DBA_RED_DBB_FED;
    // EPwm1Regs.DBCTL.all = 0x002B;//2B   //0000 0000 0010 1011
    // Dead-Band Generator Control register
    // bit15-6   0000:   reserved
    // bit5-4    10:     IN_MODE, select input source for RED and FED, EPWMxA/EPWMxB
    //  00 = A->both, 01 = B->RED and A->FED,
    //  10 = A->RED and B->FED, 11 = B->both
    // bit3-2    10:     POLSEL, polarity select for output, 00 = AH, 01 = ALC,
    // 10 = AHC, 11 = AL
    // bit1-0    11:     OUT_MODE, 00 = no DB, 01 = No RED, 10 = No FED,
    // 11 = DB fully enabled
    EPwm1Regs.AQCSFRC.bit.CSFA = 0x1;
    EPwm1Regs.AQCSFRC.bit.CSFB = 0x2;
    // 00 Forcing disabled, i.e., has no effect
    // 01 Forces a continuous low on output B
    // 10 Forces a continuous high on output B
    // 11 Software forcing is disabled and has no effect

    EPwm2Regs.DBCTL.bit.OUT_MODE = DB_FULL_ENABLE;
    EPwm2Regs.DBCTL.bit.POLSEL = DB_ACTV_HI;   // DB_ACTV_HIC;
    EPwm2Regs.DBCTL.bit.IN_MODE = DBA_ALL;     // DBA_RED_DBB_FED;
    // EPwm2Regs.DBCTL.all = 0x002B;

    EPwm2Regs.AQCSFRC.bit.CSFA = 0x1;
    EPwm2Regs.AQCSFRC.bit.CSFB = 0x2;
    // 00 Forcing disabled, i.e., has no effect
    // 01 Forces a continuous low on output B
    // 10 Forces a continuous high on output B
    // 11 Software forcing is disabled and has no effect

    EPwm3Regs.DBCTL.bit.OUT_MODE = DB_FULL_ENABLE;
    EPwm3Regs.DBCTL.bit.POLSEL = DB_ACTV_HI;   // DB_ACTV_HIC;
    EPwm3Regs.DBCTL.bit.IN_MODE = DBA_ALL;     // DBA_RED_DBB_FED;
    // EPwm3Regs.DBCTL.all = 0x002B;

    EPwm3Regs.AQCSFRC.bit.CSFA = 0x1;
    EPwm3Regs.AQCSFRC.bit.CSFB = 0x2;
    // 00 Forcing disabled, i.e., has no effect
    // 01 Forces a continuous low on output B
    // 10 Forces a continuous high on output B
    // 11 Software forcing is disabled and has no effect

    EPwm4Regs.DBCTL.bit.OUT_MODE = DB_FULL_ENABLE;
    EPwm4Regs.DBCTL.bit.POLSEL = DB_ACTV_HI;   // DB_ACTV_HIC;
    EPwm4Regs.DBCTL.bit.IN_MODE = DBA_ALL;     // DBA_RED_DBB_FED;
    // EPwm4Regs.DBCTL.all = 0x002B;

    EPwm4Regs.AQCSFRC.bit.CSFA = 0x1;
    EPwm4Regs.AQCSFRC.bit.CSFB = 0x2;
    // 00 Forcing disabled, i.e., has no effect
    // 01 Forces a continuous low on output B
    // 10 Forces a continuous high on output B
    // 11 Software forcing is disabled and has no effect

    EPwm5Regs.DBCTL.bit.OUT_MODE = DB_FULL_ENABLE;
    EPwm5Regs.DBCTL.bit.POLSEL = DB_ACTV_HI;   // DB_ACTV_HIC;
    EPwm5Regs.DBCTL.bit.IN_MODE = DBA_ALL;     // DBA_RED_DBB_FED;
    // EPwm5Regs.DBCTL.all = 0x002B;

    EPwm5Regs.AQCSFRC.bit.CSFA = 0x1;
    EPwm5Regs.AQCSFRC.bit.CSFB = 0x2;
    // 00 Forcing disabled, i.e., has no effect
    // 01 Forces a continuous low on output B
    // 10 Forces a continuous high on output B
    // 11 Software forcing is disabled and has no effect

    EPwm6Regs.DBCTL.bit.OUT_MODE = DB_FULL_ENABLE;
    EPwm6Regs.DBCTL.bit.POLSEL = DB_ACTV_HI;   // DB_ACTV_HIC;
    EPwm6Regs.DBCTL.bit.IN_MODE = DBA_ALL;     // DBA_RED_DBB_FED;
    // EPwm6Regs.DBCTL.all = 0x002B;

    EPwm6Regs.AQCSFRC.bit.CSFA = 0x1;
    EPwm6Regs.AQCSFRC.bit.CSFB = 0x2;
    // 00 Forcing disabled, i.e., has no effect
    // 01 Forces a continuous low on output B
    // 10 Forces a continuous high on output B
    // 11 Software forcing is disabled and has no effect

} // end of InitEPwm()
/**
 * @brief  初始化ePWM GPIO引脚配置 - 配置GPIO引脚为ePWM功能
 * @note   DSP2833x的每个GPIO引脚都可以配置为：
 *         - GPIO功能(默认)
 *         - 最多3种不同的外设功能
 *         复位后所有引脚默认为GPIO输入模式
 *
 *         10KW逆变器PWM引脚分配：
 *         - ePWM1A/B: GPIO0/1   (U相上下桥臂)
 *         - ePWM2A/B: GPIO2/3   (V相上下桥臂)
 *         - ePWM3A/B: GPIO4/5   (W相上下桥臂)
 *         - ePWM4A/B: GPIO6/7   (备用或辅助功能)
 *         - ePWM5A/B: GPIO8/9   (备用或辅助功能)
 *         - ePWM6A/B: GPIO10/11 (备用或辅助功能)
 * @param  None
 * @retval None
 */
void InitEPwmGpio(void)
{
    InitEPwm1Gpio();                               // 初始化ePWM1的GPIO引脚(U相)
    InitEPwm2Gpio();                               // 初始化ePWM2的GPIO引脚(V相)
    InitEPwm3Gpio();                               // 初始化ePWM3的GPIO引脚(W相)
#if DSP28_EPWM4
    InitEPwm4Gpio();                               // 初始化ePWM4的GPIO引脚(如果支持)
#endif // endif DSP28_EPWM4
#if DSP28_EPWM5
    InitEPwm5Gpio();                               // 初始化ePWM5的GPIO引脚(如果支持)
#endif // endif DSP28_EPWM5
#if DSP28_EPWM6
    InitEPwm6Gpio();                               // 初始化ePWM6的GPIO引脚(如果支持)
#endif // endif DSP28_EPWM6
}

/**
 * @brief  初始化ePWM1的GPIO引脚配置 - U相桥臂控制信号
 * @note   配置GPIO0和GPIO1为ePWM1A和ePWM1B功能
 *         - GPIO0 → EPWM1A (U相上桥臂控制信号)
 *         - GPIO1 → EPWM1B (U相下桥臂控制信号)
 * @param  None
 * @retval None
 */
void InitEPwm1Gpio(void)
{
    EALLOW;                                        // 使能对保护寄存器的写访问

    /*
     * 使能内部上拉电阻
     * 上拉电阻可以防止引脚悬空，提高抗干扰能力
     * 在PWM应用中，上拉电阻确保在未初始化时引脚为高电平(安全状态)
     */
    GpioCtrlRegs.GPAPUD.bit.GPIO0 = 0;             // 使能GPIO0内部上拉电阻(EPWM1A)
    GpioCtrlRegs.GPAPUD.bit.GPIO1 = 0;             // 使能GPIO1内部上拉电阻(EPWM1B)

    /*
     * 配置GPIO引脚复用功能
     * GPAMUX1寄存器控制GPIO0-15的功能选择
     * 每个GPIO有4种功能选择：00=GPIO, 01=外设功能1, 10=外设功能2, 11=外设功能3
     * 对于ePWM功能，需要设置为01(外设功能1)
     */
    GpioCtrlRegs.GPAMUX1.bit.GPIO0 = 1;            // 配置GPIO0为EPWM1A功能
    GpioCtrlRegs.GPAMUX1.bit.GPIO1 = 1;            // 配置GPIO1为EPWM1B功能

    EDIS;                                          // 禁用对保护寄存器的写访问
}

//
// InitEPwm2Gpio - This function initializes GPIO pins to function as ePWM2
//
void InitEPwm2Gpio(void)
{
    EALLOW;

    //
    // Enable internal pull-up for the selected pins
    // Pull-ups can be enabled or disabled by the user.
    // This will enable the pullups for the specified pins.
    // Comment out other unwanted lines.
    //
    GpioCtrlRegs.GPAPUD.bit.GPIO2 = 0; // Enable pull-up on GPIO2 (EPWM2A)
    GpioCtrlRegs.GPAPUD.bit.GPIO3 = 0; // Enable pull-up on GPIO3 (EPWM3B)

    //
    // Configure ePWM-2 pins using GPIO regs
    // This specifies which of the possible GPIO pins will be ePWM2 functional
    // pins. Comment out other unwanted lines.
    //
    GpioCtrlRegs.GPAMUX1.bit.GPIO2 = 1; // Configure GPIO2 as EPWM2A
    GpioCtrlRegs.GPAMUX1.bit.GPIO3 = 1; // Configure GPIO3 as EPWM2B

    EDIS;
}

//
// InitEPwm3Gpio - This function initializes GPIO pins to function as ePWM3
//
void InitEPwm3Gpio(void)
{
    EALLOW;

    //
    // Enable internal pull-up for the selected pins
    // Pull-ups can be enabled or disabled by the user.
    // This will enable the pullups for the specified pins.
    // Comment out other unwanted lines.
    //
    GpioCtrlRegs.GPAPUD.bit.GPIO4 = 0; // Enable pull-up on GPIO4 (EPWM3A)
    GpioCtrlRegs.GPAPUD.bit.GPIO5 = 0; // Enable pull-up on GPIO5 (EPWM3B)

    //
    // Configure ePWM-3 pins using GPIO regs
    // This specifies which of the possible GPIO pins will be ePWM3 functional
    // pins. Comment out other unwanted lines.
    //
    GpioCtrlRegs.GPAMUX1.bit.GPIO4 = 1; // Configure GPIO4 as EPWM3A
    GpioCtrlRegs.GPAMUX1.bit.GPIO5 = 1; // Configure GPIO5 as EPWM3B

    EDIS;
}

#if DSP28_EPWM4
//
// InitEPwm4Gpio - This function initializes GPIO pins to function as ePWM4
//
void InitEPwm4Gpio(void)
{
    EALLOW;

    //
    // Enable internal pull-up for the selected pins
    // Pull-ups can be enabled or disabled by the user.
    // This will enable the pullups for the specified pins.
    // Comment out other unwanted lines.
    //
    GpioCtrlRegs.GPAPUD.bit.GPIO6 = 0; // Enable pull-up on GPIO6 (EPWM4A)
    GpioCtrlRegs.GPAPUD.bit.GPIO7 = 0; // Enable pull-up on GPIO7 (EPWM4B)

    //
    // Configure ePWM-4 pins using GPIO regs
    // This specifies which of the possible GPIO pins will be ePWM4 functional
    // pins. Comment out other unwanted lines.
    //
    GpioCtrlRegs.GPAMUX1.bit.GPIO6 = 1; // Configure GPIO6 as EPWM4A
    GpioCtrlRegs.GPAMUX1.bit.GPIO7 = 1; // Configure GPIO7 as EPWM4B

    EDIS;
}
#endif // endif DSP28_EPWM4

#if DSP28_EPWM5
//
// InitEPwm5Gpio - This function initializes GPIO pins to function as ePWM5
//
void InitEPwm5Gpio(void)
{
    EALLOW;

    //
    // Enable internal pull-up for the selected pins
    // Pull-ups can be enabled or disabled by the user.
    // This will enable the pullups for the specified pins.
    // Comment out other unwanted lines.
    //
    GpioCtrlRegs.GPAPUD.bit.GPIO8 = 0; // Enable pull-up on GPIO8 (EPWM5A)
    GpioCtrlRegs.GPAPUD.bit.GPIO9 = 0; // Enable pull-up on GPIO9 (EPWM5B)

    //
    // Configure ePWM-5 pins using GPIO regs
    // This specifies which of the possible GPIO pins will be ePWM5 functional
    // pins. Comment out other unwanted lines.
    //
    GpioCtrlRegs.GPAMUX1.bit.GPIO8 = 1; // Configure GPIO8 as EPWM5A
    GpioCtrlRegs.GPAMUX1.bit.GPIO9 = 1; // Configure GPIO9 as EPWM5B

    EDIS;
}
#endif // endif DSP28_EPWM5

#if DSP28_EPWM6
//
// InitEPwm6Gpio - This function initializes GPIO pins to function as ePWM6
//
void InitEPwm6Gpio(void)
{
    EALLOW;

    //
    // Enable internal pull-up for the selected pins
    // Pull-ups can be enabled or disabled by the user.
    // This will enable the pullups for the specified pins.
    // Comment out other unwanted lines.
    //
    GpioCtrlRegs.GPAPUD.bit.GPIO10 = 0; // Enable pull-up on GPIO10 (EPWM6A)
    GpioCtrlRegs.GPAPUD.bit.GPIO11 = 0; // Enable pull-up on GPIO11 (EPWM6B)

    //
    // Configure ePWM-6 pins using GPIO regs
    // This specifies which of the possible GPIO pins will be ePWM6 functional
    // pins. Comment out other unwanted lines.
    //
    GpioCtrlRegs.GPAMUX1.bit.GPIO10 = 1; // Configure GPIO10 as EPWM6A
    GpioCtrlRegs.GPAMUX1.bit.GPIO11 = 1; // Configure GPIO11 as EPWM6B

    EDIS;
}
#endif // endif DSP28_EPWM6

//
// InitEPwmSyncGpio - This function initializes GPIO pins to function as ePWM
// Synch pins
//
void InitEPwmSyncGpio(void)
{
    EALLOW;

    //
    // Configure EPWMSYNCI
    //

    //
    // Enable internal pull-up for the selected pins
    // Pull-ups can be enabled or disabled by the user.
    // This will enable the pullups for the specified pins.
    // Comment out other unwanted lines.
    //
    GpioCtrlRegs.GPAPUD.bit.GPIO6 = 0; // Enable pull-up on GPIO6 (EPWMSYNCI)
    // GpioCtrlRegs.GPBPUD.bit.GPIO32 = 0;//Enable pull-up on GPIO32 (EPWMSYNCI)

    //
    // Set qualification for selected pins to asynch only
    // This will select synch to SYSCLKOUT for the selected pins.
    // Comment out other unwanted lines.
    //

    //
    // Synch to SYSCLKOUT GPIO6 (EPWMSYNCI)
    //
    GpioCtrlRegs.GPAQSEL1.bit.GPIO6 = 0;

    //
    // Synch to SYSCLKOUT GPIO32 (EPWMSYNCI)
    //
    // GpioCtrlRegs.GPBQSEL1.bit.GPIO32 = 0;

    //
    // Configure EPwmSync pins using GPIO regs
    // This specifies which of the possible GPIO pins will be EPwmSync
    // functional pins. Comment out other unwanted lines.
    //
    GpioCtrlRegs.GPAMUX1.bit.GPIO6 = 2; // Enable pull-up on GPIO6(EPWMSYNCI)
    // GpioCtrlRegs.GPBMUX1.bit.GPIO32 = 2;//Enable pull-up on GPIO32(EPWMSYNCI)

    //
    // Configure EPWMSYNC0
    //

    //
    // Enable internal pull-up for the selected pins
    // Pull-ups can be enabled or disabled by the user.
    // This will enable the pullups for the specified pins.
    // Comment out other unwanted lines.
    //

    //
    // Enable pull-up on GPIO6 (EPWMSYNC0)
    //
    // GpioCtrlRegs.GPAPUD.bit.GPIO6 = 0;

    //
    // Enable pull-up on GPIO33 (EPWMSYNC0)
    //
    GpioCtrlRegs.GPBPUD.bit.GPIO33 = 0;

    //
    // Enable pull-up on GPIO6 (EPWMSYNC0)
    //
    // GpioCtrlRegs.GPAMUX1.bit.GPIO6 = 3;

    //
    // Enable pull-up on GPIO33 (EPWMSYNC0)
    //
    GpioCtrlRegs.GPBMUX1.bit.GPIO33 = 2;
}

// InitTzGpio -  This function initializes GPIO pins to function as Trip Zone
void InitTzGpio(void)
{
}

/**
 * @brief  使能PWM输出 - 启动逆变器PWM信号输出
 * @note   按照安全时序启动PWM输出：
 *         1. 先释放下桥臂强制输出(CSFB)，使下桥臂可以正常工作
 *         2. 延时10us确保下桥臂稳定
 *         3. 再释放上桥臂强制输出(CSFA)，使上桥臂可以正常工作
 *         这样的时序可以避免上下桥臂同时导通造成直通故障
 * @param  None
 * @retval None
 * @safety 此函数涉及功率器件控制，调用前必须确保：
 *         - 系统已完成初始化
 *         - 保护电路正常工作
 *         - 输出电容已预充电
 */
void PWMOutputsEnable(void)
{   // start of PWMOutputsEnable()
    // HWI_disable(); // 可选：禁用硬件中断，确保原子操作

    /*
     * 第一步：释放所有下桥臂(B通道)的软件强制输出
     * CSFB = 0x0: 禁用软件强制，恢复正常PWM控制
     */
    EPwm1Regs.AQCSFRC.bit.CSFB = 0x0;             // ePWM1B(U相下桥臂)恢复正常PWM
    EPwm2Regs.AQCSFRC.bit.CSFB = 0x0;             // ePWM2B(V相下桥臂)恢复正常PWM
    EPwm3Regs.AQCSFRC.bit.CSFB = 0x0;             // ePWM3B(W相下桥臂)恢复正常PWM
    EPwm4Regs.AQCSFRC.bit.CSFB = 0x0;             // ePWM4B恢复正常PWM
    EPwm5Regs.AQCSFRC.bit.CSFB = 0x0;             // ePWM5B恢复正常PWM
    EPwm6Regs.AQCSFRC.bit.CSFB = 0x0;             // ePWM6B恢复正常PWM

    extern void DSP28x_usDelay(Uint32 Count);
    DELAY_US(10);                                  // 延时10微秒，确保下桥臂状态稳定

    /*
     * 第二步：释放所有上桥臂(A通道)的软件强制输出
     * CSFA = 0x0: 禁用软件强制，恢复正常PWM控制
     */
    EPwm1Regs.AQCSFRC.bit.CSFA = 0x0;             // ePWM1A(U相上桥臂)恢复正常PWM
    EPwm2Regs.AQCSFRC.bit.CSFA = 0x0;             // ePWM2A(V相上桥臂)恢复正常PWM
    EPwm3Regs.AQCSFRC.bit.CSFA = 0x0;             // ePWM3A(W相上桥臂)恢复正常PWM
    EPwm4Regs.AQCSFRC.bit.CSFA = 0x0;             // ePWM4A恢复正常PWM
    EPwm5Regs.AQCSFRC.bit.CSFA = 0x0;             // ePWM5A恢复正常PWM
    EPwm6Regs.AQCSFRC.bit.CSFA = 0x0;             // ePWM6A恢复正常PWM

    // HWI_enable(); // 可选：重新使能硬件中断

} // end of PWMOutputsEnable()

/**
 * @brief  禁用PWM输出 - 安全关闭逆变器PWM信号输出
 * @note   按照安全时序关闭PWM输出：
 *         1. 先关闭Boost升压电路(如果有)
 *         2. 强制所有上桥臂输出低电平(CSFA=0x1)
 *         3. 延时10us确保上桥臂完全关断
 *         4. 强制所有下桥臂输出高电平(CSFB=0x2)
 *         这样的时序确保功率器件安全关断，避免悬空状态
 * @param  None
 * @retval None
 * @safety 紧急停机函数，用于故障保护和系统关闭
 * @called_by Main.c, MainStatusMachine.c, 故障保护中断
 */
void PWMOutputsDisable(void)
{
    /*
     * 第一步：关闭Boost升压电路
     * 将eCAP模块的比较值设为0，停止Boost PWM输出
     * 这通常用于PFC(功率因数校正)电路的控制
     */
    ECap1Regs.CAP4 = 0;                            // 关闭eCAP1 Boost PWM输出
    ECap2Regs.CAP4 = 0;                            // 关闭eCAP2 Boost PWM输出
    ECap3Regs.CAP4 = 0;                            // 关闭eCAP3 Boost PWM输出
    ECap4Regs.CAP4 = 0;                            // 关闭eCAP4 Boost PWM输出

    /*
     * 第二步：强制所有上桥臂(A通道)输出低电平
     * CSFA = 0x1: 强制输出低电平，确保上桥臂MOSFET/IGBT关断
     */
    EPwm1Regs.AQCSFRC.bit.CSFA = 0x1;             // 强制ePWM1A(U相上桥臂)输出低电平
    EPwm2Regs.AQCSFRC.bit.CSFA = 0x1;             // 强制ePWM2A(V相上桥臂)输出低电平
    EPwm3Regs.AQCSFRC.bit.CSFA = 0x1;             // 强制ePWM3A(W相上桥臂)输出低电平
    EPwm4Regs.AQCSFRC.bit.CSFA = 0x1;             // 强制ePWM4A输出低电平
    EPwm5Regs.AQCSFRC.bit.CSFA = 0x1;             // 强制ePWM5A输出低电平
    EPwm6Regs.AQCSFRC.bit.CSFA = 0x1;             // 强制ePWM6A输出低电平

    extern void DSP28x_usDelay(Uint32 Count);
    DELAY_US(10);                                  // 延时10微秒，确保上桥臂完全关断

    /*
     * 第三步：强制所有下桥臂(B通道)输出高电平
     * CSFB = 0x2: 强制输出高电平，确保下桥臂MOSFET/IGBT也关断
     * 注意：这里设置为高电平是因为驱动电路可能是反相的
     */
    EPwm1Regs.AQCSFRC.bit.CSFB = 0x2;             // 强制ePWM1B(U相下桥臂)输出高电平
    EPwm2Regs.AQCSFRC.bit.CSFB = 0x2;             // 强制ePWM2B(V相下桥臂)输出高电平
    EPwm3Regs.AQCSFRC.bit.CSFB = 0x2;             // 强制ePWM3B(W相下桥臂)输出高电平
    EPwm4Regs.AQCSFRC.bit.CSFB = 0x2;             // 强制ePWM4B输出高电平
    EPwm5Regs.AQCSFRC.bit.CSFB = 0x2;             // 强制ePWM5B输出高电平
    EPwm6Regs.AQCSFRC.bit.CSFB = 0x2;             // 强制ePWM6B输出高电平
}

//
// End of file
//
